import torch
import torch.nn.functional as F
import random

flag_imbalanced_contrastive_loss = False
flag_imbalanced_weight_reverse = False
flag_print_loss_weight = False


def sim(x, y):
    norm_x = F.normalize(x, dim=-1)
    norm_y = F.normalize(y, dim=-1)
    return torch.matmul(norm_x, norm_y.transpose(1, 0))


def flat_contrastive_loss_func(label_sim, hier_labels, processor, output_at_mask, imbalanced_weight=False, depth=2,
                              contrastive_level=1, imbalanced_weight_reverse=True, use_cuda=True, temperature=0.1):
    """
    改进的层次对比损失函数

    Args:
        label_sim: 标签相似度矩阵 [num_labels, num_labels]
        hier_labels: 层次标签列表，每个元素是一个层级的标签 [depth, batch_size]
        processor: 数据处理器
        output_at_mask: 特征表示 [batch_size, depth, hidden_dim]
        imbalanced_weight: 是否使用不平衡权重
        depth: 层次深度
        contrastive_level: 对比学习级别
        imbalanced_weight_reverse: 是否反转权重
        use_cuda: 是否使用CUDA
        temperature: 温度参数
    """
    global flag_imbalanced_contrastive_loss, flag_imbalanced_weight_reverse, flag_print_loss_weight

    if use_cuda:
        output_at_mask = output_at_mask.cuda()

    cur_batch_size = output_at_mask.shape[0]
    assert cur_batch_size == len(hier_labels[0])

    # 计算层次权重
    if not imbalanced_weight:
        loss_weight = [1.0 for _ in range(depth)]
    else:
        if not flag_imbalanced_contrastive_loss:
            print(f"using imbalanced contrastive loss with contrastive_level:{contrastive_level}")
            flag_imbalanced_contrastive_loss = True
        loss_weight = [1.0 / (2 ** (i * contrastive_level)) for i in range(depth)]

    if imbalanced_weight_reverse:
        if not flag_imbalanced_weight_reverse:
            print("imbalanced weight reversed")
            flag_imbalanced_weight_reverse = True
        loss_weight.reverse()

    if not flag_print_loss_weight:
        print("loss_weight:", loss_weight)
        flag_print_loss_weight = True

    total_loss = 0.0

    # 对每个层次计算对比损失
    for depth_idx in range(depth):
        # 获取当前层次的特征表示 [batch_size, hidden_dim]
        cur_features = output_at_mask[:, depth_idx, :]
        cur_labels = hier_labels[depth_idx]

        # 计算相似度矩阵 [batch_size, batch_size]
        similarity_matrix = sim(cur_features, cur_features) / temperature

        # 构建正负样本掩码矩阵
        positive_mask, negative_mask = build_contrastive_mask(
            cur_labels, cur_batch_size, label_sim, use_cuda
        )

        # 计算对比损失
        depth_loss = compute_contrastive_loss(
            similarity_matrix, positive_mask, negative_mask, cur_batch_size
        )

        # 加权累加
        total_loss += depth_loss * loss_weight[depth_idx]

    return total_loss / cur_batch_size


def build_contrastive_mask(labels, batch_size, label_sim=None, use_cuda=True):
    """
    构建对比学习的正负样本掩码矩阵

    Args:
        labels: 当前层次的标签 [batch_size]
        batch_size: 批次大小
        label_sim: 标签相似度矩阵（可选）
        use_cuda: 是否使用CUDA

    Returns:
        positive_mask: 正样本掩码 [batch_size, batch_size]
        negative_mask: 负样本掩码 [batch_size, batch_size]
    """
    device = 'cuda' if use_cuda else 'cpu'
    positive_mask = torch.zeros(batch_size, batch_size, device=device)
    negative_mask = torch.zeros(batch_size, batch_size, device=device)

    # 构建基本的正负样本掩码
    for i in range(batch_size):
        for j in range(batch_size):
            if i == j:
                continue  # 跳过自己

            label_i = labels[i]
            label_j = labels[j]

            # 处理多标签情况
            if isinstance(label_i, (list, tuple)) and isinstance(label_j, (list, tuple)):
                # 如果有交集，则为正样本
                if len(set(label_i).intersection(set(label_j))) > 0:
                    positive_mask[i][j] = 1
                else:
                    negative_mask[i][j] = 1
            elif isinstance(label_i, (list, tuple)):
                if label_j in label_i:
                    positive_mask[i][j] = 1
                else:
                    negative_mask[i][j] = 1
            elif isinstance(label_j, (list, tuple)):
                if label_i in label_j:
                    positive_mask[i][j] = 1
                else:
                    negative_mask[i][j] = 1
            else:
                # 单标签情况
                if label_i == label_j:
                    positive_mask[i][j] = 1
                else:
                    negative_mask[i][j] = 1

    # 可选：基于标签相似度进行硬负样本挖掘
    if label_sim is not None:
        negative_mask = apply_hard_negative_mining(
            labels, negative_mask, label_sim, batch_size, top_k=10
        )

    return positive_mask, negative_mask


def apply_hard_negative_mining(labels, negative_mask, label_sim, batch_size, top_k=10):
    """
    应用硬负样本挖掘

    Args:
        labels: 标签列表
        negative_mask: 负样本掩码
        label_sim: 标签相似度矩阵
        batch_size: 批次大小
        top_k: 选择的硬负样本数量

    Returns:
        updated_negative_mask: 更新后的负样本掩码
    """
    updated_mask = negative_mask.clone()

    for i in range(batch_size):
        label_i = labels[i]

        # 确保标签索引在label_sim范围内
        if isinstance(label_i, (int, torch.Tensor)) and label_i < len(label_sim):
            # 获取与当前标签最相似的标签
            _, similar_indices = torch.sort(label_sim[label_i], descending=True)
            similar_indices = similar_indices[:top_k]

            # 在当前batch中找到对应的样本
            for j in range(batch_size):
                if i != j:
                    label_j = labels[j]
                    if isinstance(label_j, (int, torch.Tensor)) and label_j in similar_indices:
                        # 将相似的负样本权重降低（设为0表示忽略）
                        updated_mask[i][j] = 0

    return updated_mask


def compute_contrastive_loss(similarity_matrix, positive_mask, negative_mask, batch_size, eps=1e-8):
    """
    计算对比损失

    Args:
        similarity_matrix: 相似度矩阵 [batch_size, batch_size]
        positive_mask: 正样本掩码 [batch_size, batch_size]
        negative_mask: 负样本掩码 [batch_size, batch_size]
        batch_size: 批次大小
        eps: 数值稳定性参数

    Returns:
        contrastive_loss: 对比损失
    """
    # 应用exp函数
    exp_sim = torch.exp(similarity_matrix)

    total_loss = 0.0
    valid_samples = 0

    for i in range(batch_size):
        # 获取正样本和负样本的相似度
        pos_sim = exp_sim[i] * positive_mask[i]
        neg_sim = exp_sim[i] * negative_mask[i]

        # 计算正样本和负样本的总和
        pos_sum = pos_sim.sum()
        neg_sum = neg_sim.sum()

        # 如果没有正样本，跳过这个样本
        if pos_sum == 0:
            continue

        # 计算InfoNCE损失
        denominator = pos_sum + neg_sum + eps
        loss = -torch.log(pos_sum / denominator + eps)

        total_loss += loss
        valid_samples += 1

    if valid_samples == 0:
        return torch.tensor(0.0, device=similarity_matrix.device)

    return total_loss / valid_samples


def constraint_multi_depth_loss_func(logits, loss_func, hier_labels, processor, args, use_cuda=True, mode=0):
    if isinstance(logits, list):
        leaf_logits = logits[-1]
    elif isinstance(logits, torch.Tensor):
        leaf_logits = logits[:, -1, :]
    contrastive_level = 0
    hier_mapping = processor.hier_mapping
    flat_slot2value = processor.flat_slot2value
    # batch_size * label_size(134)
    depth = len(hier_mapping) + 1

    loss_weight = [1 / 2 ** (i * contrastive_level) for i in range(depth - 1)]

    leaf_logits = torch.softmax(leaf_logits, dim=-1)
    hier_logits = []
    hier_logits.insert(0, leaf_logits)

    batch_s = leaf_logits.shape[0]
    constraint_loss = 0

    all_length = len(processor.all_labels)
    for depth_idx in range(depth - 2, -1, -1):
        if isinstance(logits, list):
            ori_logits = logits[depth_idx]
        elif isinstance(logits, torch.Tensor):
            ori_logits = logits[:, depth_idx, :]
        ## True
        if args.multi_verb:
            cur_logits = torch.zeros(batch_s, len(processor.label_list[depth_idx]))

            for i in range(cur_logits.shape[-1]):
                # sum
                cur_logits[:, i] = torch.sum(hier_logits[0][:, list(hier_mapping[depth_idx][0][i])], dim=-1)
                # mean
                # cur_logits[:, i] = torch.mean(hier_logits[0][:, list(hier_mapping[depth_idx][0][i])], dim=-1)
        else:
            cur_logits = torch.zeros(batch_s, all_length)
            cd_labels = processor.depth2label[depth_idx]
            for i in range(all_length):
                if i in cd_labels:
                    cur_logits[:, i] = torch.sum(hier_logits[0][:, list(flat_slot2value[i])], dim=-1)
            # ver.weight.shape  [7+ 64 + 140, 768]
        cur_labels = hier_labels[depth_idx]

        if use_cuda:
            cur_logits = cur_logits.cuda()
            cur_labels = cur_labels.cuda()
        # default mode = 0
        if mode:
            cur_logits = cur_logits + ori_logits

        if args.multi_label:
            cur_multi_label = torch.zeros_like(cur_logits)
            for i in range(cur_multi_label.shape[0]):
                cur_multi_label[i][cur_labels[i]] = 1
            cur_labels = cur_multi_label

            # cur_logits = torch.softmax(cur_logits, dim=-1)
        hier_logits.insert(0, cur_logits)
        cur_constraint_loss = loss_func(cur_logits, cur_labels)
        constraint_loss += cur_constraint_loss * loss_weight[depth_idx]
    return constraint_loss


def multi_path_constraint_multi_depth_loss_func(logits, loss_func, hier_labels, processor, args, use_cuda=True, mode=0):
    contrastive_level = 0
    hier_mapping = processor.hier_mapping

    depth = len(hier_mapping) + 1

    loss_weight = [1 / 2 ** (i * contrastive_level) for i in range(depth - 1)]

    batch_s = logits[0].shape[0]
    constraint_loss = 0

    for depth_idx in range(depth - 2, -1, -1):
        if isinstance(logits, list):
            pre_logits = logits[depth_idx+1]
            ori_logits = logits[depth_idx]
        elif isinstance(logits, torch.Tensor):
            pre_logits = logits[:, depth_idx+1, :]
            ori_logits = logits[:, depth_idx, :]
        else:
            print(type(logits))
            raise TypeError
        cur_logits = torch.zeros(batch_s, len(processor.label_list[depth_idx]))

        for i in range(cur_logits.shape[-1]):
            ## sum
            # cur_logits[:, i] = torch.sum(hier_logits[0][:, list(hier_mapping[depth_idx][0][i])], dim=-1)
            ## mean
            if len(hier_mapping[depth_idx][0][i]) != 0:
                # ori_logits[:, i] = torch.mean(pre_logits[:, list(hier_mapping[depth_idx][0][i])], dim=-1)
                # ori_logits[:, i] = torch.mean(ori_logits[:, i] + torch.mean(pre_logits[:, list(hier_mapping[depth_idx][0][i])], dim=-1), dim=-1)
                ori_logits[:, i] = ori_logits[:, i] * 0.99 + torch.mean(pre_logits[:, list(hier_mapping[depth_idx][0][i])], dim=-1) * 0.01

        cur_labels = hier_labels[depth_idx]

        if use_cuda:
            ori_logits = ori_logits.cuda()

        cur_multi_label = torch.zeros_like(cur_logits).to("cuda:0")
        for i in range(cur_multi_label.shape[0]):
            for j in cur_labels[i]:
                cur_multi_label[i][j] = 1
        cur_labels = cur_multi_label

        cur_constraint_loss = loss_func(ori_logits, cur_labels)
        constraint_loss += cur_constraint_loss * loss_weight[depth_idx]
    return constraint_loss
